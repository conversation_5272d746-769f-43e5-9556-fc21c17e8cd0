'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useLanguage } from '@/contexts/LanguageContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AuthModal from '@/components/AuthModal';
import AdSense from '@/components/AdSense';

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  category: string;
  readTime: string;
  publishDate: string;
  slug: string;
  featured: boolean;
}

export default function BlogClient() {
  const router = useRouter();
  const { language } = useLanguage();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const blogPosts: BlogPost[] = [
    {
      id: '1',
      title: '10 Essential Tips for Learning English Faster',
      excerpt: 'Discover proven strategies to accelerate your English learning journey and achieve fluency faster.',
      category: 'Tips',
      readTime: '5 min read',
      publishDate: '2024-01-15',
      slug: '10-essential-tips-learning-english-faster',
      featured: true
    },
    {
      id: '2',
      title: 'Common English Grammar Mistakes to Avoid',
      excerpt: 'Learn about the most frequent grammar errors made by English learners and how to fix them.',
      category: 'Grammar',
      readTime: '7 min read',
      publishDate: '2024-01-12',
      slug: 'common-english-grammar-mistakes-avoid',
      featured: true
    },
    {
      id: '3',
      title: 'How to Improve Your English Pronunciation',
      excerpt: 'Master English pronunciation with these practical techniques and exercises.',
      category: 'Pronunciation',
      readTime: '6 min read',
      publishDate: '2024-01-10',
      slug: 'improve-english-pronunciation-guide',
      featured: false
    },
    {
      id: '4',
      title: 'Building Vocabulary: Effective Strategies',
      excerpt: 'Learn the best methods to expand your English vocabulary and remember new words.',
      category: 'Vocabulary',
      readTime: '8 min read',
      publishDate: '2024-01-08',
      slug: 'building-vocabulary-effective-strategies',
      featured: false
    },
    {
      id: '5',
      title: 'English Idioms and Their Meanings',
      excerpt: 'Understand common English idioms and how to use them in everyday conversation.',
      category: 'Culture',
      readTime: '10 min read',
      publishDate: '2024-01-05',
      slug: 'english-idioms-meanings-guide',
      featured: false
    },
    {
      id: '6',
      title: 'Preparing for English Job Interviews',
      excerpt: 'Get ready for English job interviews with these essential phrases and tips.',
      category: 'Business',
      readTime: '9 min read',
      publishDate: '2024-01-03',
      slug: 'preparing-english-job-interviews',
      featured: false
    },
    {
      id: '7',
      title: 'English for Travel: Essential Phrases',
      excerpt: 'Learn must-know English phrases for traveling and communicating abroad.',
      category: 'Travel',
      readTime: '6 min read',
      publishDate: '2024-01-01',
      slug: 'english-travel-essential-phrases',
      featured: false
    },
    {
      id: '8',
      title: 'Understanding American vs British English',
      excerpt: 'Explore the differences between American and British English in vocabulary and pronunciation.',
      category: 'Culture',
      readTime: '8 min read',
      publishDate: '2023-12-28',
      slug: 'american-vs-british-english-differences',
      featured: false
    }
  ];

  const categories = ['all', 'Tips', 'Grammar', 'Vocabulary', 'Pronunciation', 'Culture', 'Business', 'Travel'];

  const filteredPosts = selectedCategory === 'all' 
    ? blogPosts 
    : blogPosts.filter(post => post.category === selectedCategory);

  const featuredPosts = blogPosts.filter(post => post.featured);

  const getPageTitle = () => {
    switch (language) {
      case 'vi':
        return 'Blog Học Tiếng Anh';
      case 'zh':
        return '英语学习博客';
      default:
        return 'English Learning Blog';
    }
  };

  const getPageDescription = () => {
    switch (language) {
      case 'vi':
        return 'Đọc các bài viết hữu ích, mẹo và hướng dẫn học tiếng Anh. Cải thiện kỹ năng tiếng Anh với lời khuyên chuyên gia.';
      case 'zh':
        return '阅读有用的文章、技巧和英语学习指南。通过专家建议提高您的英语技能。';
      default:
        return 'Read helpful articles, tips, and guides for learning English. Improve your English skills with expert advice.';
    }
  };

  const handlePostClick = (slug: string) => {
    router.push(`/blog/${slug}`);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'vi' ? 'vi-VN' : language === 'zh' ? 'zh-CN' : 'en-US');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header onAuthClick={() => setShowAuthModal(true)} />
      
      <div className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              {getPageTitle()}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {getPageDescription()}
            </p>
          </div>

          {/* Featured Posts */}
          {featuredPosts.length > 0 && (
            <div className="mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                {language === 'vi' ? 'Bài viết nổi bật' : language === 'zh' ? '精选文章' : 'Featured Posts'}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {featuredPosts.map((post) => (
                  <div
                    key={post.id}
                    onClick={() => handlePostClick(post.slug)}
                    className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1 border border-gray-100"
                  >
                    <div className="flex items-center justify-between mb-3">
                      <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                        {post.category}
                      </span>
                      <span className="text-sm text-gray-500">{post.readTime}</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2 line-clamp-2">
                      {post.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {post.excerpt}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-500">{formatDate(post.publishDate)}</span>
                      <div className="text-blue-500 text-sm font-medium">
                        {language === 'vi' ? 'Đọc thêm →' : language === 'zh' ? '阅读更多 →' : 'Read more →'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Category Filter */}
          <div className="flex justify-center mb-8">
            <div className="bg-white rounded-xl p-2 shadow-lg">
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                      selectedCategory === category
                        ? 'bg-blue-500 text-white shadow-md'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    {category === 'all' 
                      ? (language === 'vi' ? 'Tất cả' : language === 'zh' ? '全部' : 'All')
                      : category
                    }
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* AdSense */}
          <div className="mb-8">
            <AdSense />
          </div>

          {/* All Posts */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPosts.map((post) => (
              <div
                key={post.id}
                onClick={() => handlePostClick(post.slug)}
                className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1 border border-gray-100"
              >
                <div className="flex items-center justify-between mb-3">
                  <span className="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                    {post.category}
                  </span>
                  <span className="text-sm text-gray-500">{post.readTime}</span>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2 line-clamp-2">
                  {post.title}
                </h3>
                <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                  {post.excerpt}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">{formatDate(post.publishDate)}</span>
                  <div className="text-blue-500 text-sm font-medium">
                    {language === 'vi' ? 'Đọc →' : language === 'zh' ? '阅读 →' : 'Read →'}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Coming Soon Message */}
          <div className="mt-12 text-center bg-white rounded-xl p-8 shadow-lg">
            <div className="text-4xl mb-4">📝</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {language === 'vi' ? 'Nội dung đang được phát triển' : language === 'zh' ? '内容正在开发中' : 'Content Coming Soon'}
            </h3>
            <p className="text-gray-600">
              {language === 'vi' 
                ? 'Các bài viết blog chi tiết sẽ được cập nhật sớm. Hãy quay lại để đọc những bài viết hữu ích về học tiếng Anh!' 
                : language === 'zh' 
                ? '详细的博客文章即将更新。请回来阅读有用的英语学习文章！' 
                : 'Detailed blog articles will be available soon. Come back to read helpful English learning articles!'
              }
            </p>
          </div>
        </div>
      </div>

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
      
      <Footer />
    </div>
  );
}
