import { Metadata } from 'next';
import VocabularyLevelClient from '@/components/VocabularyLevelClient';

export const metadata: Metadata = {
  title: 'Intermediate Vocabulary - Infinite English',
  description: 'Expand your English vocabulary with intermediate-level words, phrases, and expressions. Perfect for improving your English fluency.',
  keywords: 'intermediate vocabulary, English vocabulary practice, intermediate English words, vocabulary building, English fluency',
  openGraph: {
    title: 'Intermediate Vocabulary - Infinite English',
    description: 'Build your English vocabulary with intermediate-level words and phrases.',
    type: 'website',
  },
};

export default function IntermediateVocabularyPage() {
  return <VocabularyLevelClient level="intermediate" />;
}
