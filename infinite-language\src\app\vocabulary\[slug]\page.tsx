import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import VocabularyTopicClient from '@/components/VocabularyTopicClient';

interface Props {
  params: { slug: string };
}

// Generate metadata for SEO
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  try {
    // Fetch topic data for metadata
    const response = await fetch(`${process.env.NEXTAUTH_URL}/api/vocabulary/topics/${params.slug}`, {
      cache: 'no-store',
    });

    if (!response.ok) {
      return {
        title: 'Topic Not Found - Infinite English',
        description: 'The vocabulary topic you are looking for could not be found.',
      };
    }

    const data = await response.json();
    const topic = data.data?.topic;

    if (!topic) {
      return {
        title: 'Topic Not Found - Infinite English',
        description: 'The vocabulary topic you are looking for could not be found.',
      };
    }

    return {
      title: topic.seoTitle || `${topic.name} Vocabulary - Infinite English`,
      description: topic.seoDescription || `Learn ${topic.name.toLowerCase()} vocabulary with definitions, examples, and pronunciation. ${topic.description}`,
      keywords: topic.seoKeywords?.join(', ') || `${topic.name}, English vocabulary, ${topic.level} level, vocabulary practice`,
      openGraph: {
        title: topic.seoTitle || `${topic.name} Vocabulary - Infinite English`,
        description: topic.seoDescription || topic.description,
        type: 'website',
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Vocabulary Topic - Infinite English',
      description: 'Learn English vocabulary with interactive exercises and examples.',
    };
  }
}

export default function VocabularyTopicPage({ params }: Props) {
  return <VocabularyTopicClient slug={params.slug} />;
}
