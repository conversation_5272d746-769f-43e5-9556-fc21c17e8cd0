import { Metadata } from 'next';
import VocabularyLevelClient from '@/components/VocabularyLevelClient';

export const metadata: Metadata = {
  title: 'Beginner Vocabulary - Infinite English',
  description: 'Learn beginner-level English vocabulary with easy words, definitions, and examples. Perfect for English language learners starting their journey.',
  keywords: 'beginner vocabulary, basic English words, elementary vocabulary, English for beginners, simple English words',
  openGraph: {
    title: 'Beginner Vocabulary - Infinite English',
    description: 'Start your English learning journey with beginner vocabulary.',
    type: 'website',
  },
};

export default function BeginnerVocabularyPage() {
  return <VocabularyLevelClient level="beginner" />;
}
