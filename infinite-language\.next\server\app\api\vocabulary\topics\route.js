/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/vocabulary/topics/route";
exports.ids = ["app/api/vocabulary/topics/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvocabulary%2Ftopics%2Froute&page=%2Fapi%2Fvocabulary%2Ftopics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvocabulary%2Ftopics%2Froute.ts&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvocabulary%2Ftopics%2Froute&page=%2Fapi%2Fvocabulary%2Ftopics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvocabulary%2Ftopics%2Froute.ts&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_AI_webngonngu_infinite_language_src_app_api_vocabulary_topics_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/vocabulary/topics/route.ts */ \"(rsc)/./src/app/api/vocabulary/topics/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/vocabulary/topics/route\",\n        pathname: \"/api/vocabulary/topics\",\n        filename: \"route\",\n        bundlePath: \"app/api/vocabulary/topics/route\"\n    },\n    resolvedPagePath: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\api\\\\vocabulary\\\\topics\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_AI_webngonngu_infinite_language_src_app_api_vocabulary_topics_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvocabulary%2Ftopics%2Froute&page=%2Fapi%2Fvocabulary%2Ftopics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvocabulary%2Ftopics%2Froute.ts&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/vocabulary/topics/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/vocabulary/topics/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _models_VocabularyTopic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/models/VocabularyTopic */ \"(rsc)/./src/models/VocabularyTopic.ts\");\n\n\n\n// GET - Get all vocabulary topics\nasync function GET(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const { searchParams } = new URL(request.url);\n        const language = searchParams.get('language') || 'en';\n        const level = searchParams.get('level');\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const page = parseInt(searchParams.get('page') || '1');\n        // Build query\n        const query = {\n            isActive: true,\n            language\n        };\n        if (level) {\n            query.level = level;\n        }\n        // Get topics with pagination\n        const skip = (page - 1) * limit;\n        const topics = await _models_VocabularyTopic__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find(query).sort({\n            order: 1,\n            createdAt: -1\n        }).skip(skip).limit(limit).lean();\n        // Get total count for pagination\n        const total = await _models_VocabularyTopic__WEBPACK_IMPORTED_MODULE_2__[\"default\"].countDocuments(query);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                topics,\n                pagination: {\n                    page,\n                    limit,\n                    total,\n                    pages: Math.ceil(total / limit)\n                }\n            }\n        });\n    } catch (error) {\n        console.error('Error fetching vocabulary topics:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch vocabulary topics'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Create new vocabulary topic (admin only)\nasync function POST(request) {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const body = await request.json();\n        const { name, slug, description, icon, color, level, language, order, seoTitle, seoDescription, seoKeywords } = body;\n        // Validation\n        if (!name || !slug || !description || !level) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // Check if slug already exists\n        const existingTopic = await _models_VocabularyTopic__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findOne({\n            slug\n        });\n        if (existingTopic) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Slug already exists'\n            }, {\n                status: 400\n            });\n        }\n        // Create new topic\n        const topic = new _models_VocabularyTopic__WEBPACK_IMPORTED_MODULE_2__[\"default\"]({\n            name,\n            slug,\n            description,\n            icon: icon || '📚',\n            color: color || '#3B82F6',\n            level,\n            language: language || 'en',\n            order: order || 0,\n            seoTitle,\n            seoDescription,\n            seoKeywords\n        });\n        await topic.save();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: topic\n        });\n    } catch (error) {\n        console.error('Error creating vocabulary topic:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to create vocabulary topic'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/vocabulary/topics/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n// Global is used here to maintain a cached connection across hot reloads in development\nlet cached = global.mongoose || {\n    conn: null,\n    promise: null\n};\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function connectDB() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (connectDB);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/VocabularyTopic.ts":
/*!***************************************!*\
  !*** ./src/models/VocabularyTopic.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst VocabularyTopicSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: true,\n        trim: true\n    },\n    slug: {\n        type: String,\n        required: true,\n        unique: true,\n        lowercase: true,\n        trim: true\n    },\n    description: {\n        type: String,\n        required: true,\n        trim: true\n    },\n    icon: {\n        type: String,\n        required: true,\n        default: '📚'\n    },\n    color: {\n        type: String,\n        required: true,\n        default: '#3B82F6'\n    },\n    level: {\n        type: String,\n        enum: [\n            'beginner',\n            'intermediate',\n            'advanced'\n        ],\n        required: true\n    },\n    language: {\n        type: String,\n        enum: [\n            'en',\n            'vi',\n            'zh'\n        ],\n        required: true,\n        default: 'en'\n    },\n    wordCount: {\n        type: Number,\n        default: 0\n    },\n    isActive: {\n        type: Boolean,\n        default: true\n    },\n    order: {\n        type: Number,\n        default: 0\n    },\n    seoTitle: {\n        type: String,\n        trim: true\n    },\n    seoDescription: {\n        type: String,\n        trim: true\n    },\n    seoKeywords: [\n        {\n            type: String,\n            trim: true\n        }\n    ]\n}, {\n    timestamps: true\n});\n// Indexes for efficient queries\nVocabularyTopicSchema.index({\n    slug: 1\n});\nVocabularyTopicSchema.index({\n    level: 1,\n    language: 1\n});\nVocabularyTopicSchema.index({\n    isActive: 1,\n    order: 1\n});\nVocabularyTopicSchema.index({\n    language: 1,\n    isActive: 1\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).VocabularyTopic || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('VocabularyTopic', VocabularyTopicSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/VocabularyTopic.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvocabulary%2Ftopics%2Froute&page=%2Fapi%2Fvocabulary%2Ftopics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvocabulary%2Ftopics%2Froute.ts&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();