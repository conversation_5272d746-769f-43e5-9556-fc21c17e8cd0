import { Metadata } from 'next';
import VocabularyLevelClient from '@/components/VocabularyLevelClient';

export const metadata: Metadata = {
  title: 'Advanced Vocabulary - Infinite English',
  description: 'Master advanced English vocabulary with sophisticated words, academic terms, and professional expressions for fluent communication.',
  keywords: 'advanced vocabulary, sophisticated English words, academic vocabulary, professional English, advanced English terms',
  openGraph: {
    title: 'Advanced Vocabulary - Infinite English',
    description: 'Master advanced English vocabulary for professional and academic success.',
    type: 'website',
  },
};

export default function AdvancedVocabularyPage() {
  return <VocabularyLevelClient level="advanced" />;
}
