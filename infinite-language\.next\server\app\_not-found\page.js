/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers.tsx */ \"(rsc)/./src/components/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"296ed102954d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcQUlcXHdlYm5nb25uZ3VcXGluZmluaXRlLWxhbmd1YWdlXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyOTZlZDEwMjk1NGRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Providers */ \"(rsc)/./src/components/Providers.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Infinite English - Học Tiếng Anh Miễn Phí với AI | Nền Tảng Học Tiếng Anh Thông Minh Cho Người Việt\",\n        template: \"%s | Infinite English\"\n    },\n    description: \"Học tiếng Anh miễn phí với nền tảng AI thông minh dành cho người Việt. Câu hỏi trắc nghiệm vô hạn, bài học có cấu trúc, từ vựng và mẹo học hiệu quả. Phù hợp mọi trình độ từ cơ bản đến nâng cao. Nền tảng được hỗ trợ bởi quảng cáo để duy trì miễn phí.\",\n    keywords: [\n        \"học tiếng anh\",\n        \"học tiếng anh miễn phí\",\n        \"học tiếng anh online\",\n        \"AI học tiếng anh\",\n        \"trắc nghiệm tiếng anh\",\n        \"bài học tiếng anh\",\n        \"từ vựng tiếng anh\",\n        \"ngữ pháp tiếng anh\",\n        \"luyện nói tiếng anh\",\n        \"luyện thi TOEIC\",\n        \"luyện thi IELTS\",\n        \"phát âm tiếng anh\",\n        \"giao tiếp tiếng anh\",\n        \"khóa học tiếng anh miễn phí\",\n        \"luyện tập tiếng anh\",\n        \"tiếng anh cơ bản\",\n        \"tiếng anh giao tiếp\",\n        \"học tiếng anh cho người mới bắt đầu\",\n        \"học tiếng anh cho người Việt\",\n        \"ứng dụng học tiếng anh\",\n        \"website học tiếng anh\",\n        \"English learning\",\n        \"learn English online\",\n        \"free English course\"\n    ],\n    authors: [\n        {\n            name: \"Infinite English Team\"\n        }\n    ],\n    creator: \"Infinite English\",\n    publisher: \"Infinite English\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL('https://inenglish.io.vn'),\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    },\n    openGraph: {\n        type: 'website',\n        locale: 'vi_VN',\n        url: 'https://inenglish.io.vn',\n        title: 'Infinite English - Học Tiếng Anh Miễn Phí với AI | Nền Tảng Học Tiếng Anh Cho Người Việt',\n        description: 'Học tiếng Anh miễn phí với nền tảng AI thông minh dành cho người Việt. Câu hỏi trắc nghiệm vô hạn, bài học có cấu trúc, từ vựng và mẹo học hiệu quả.',\n        siteName: 'Infinite English',\n        images: [\n            {\n                url: '/logo.png',\n                width: 1200,\n                height: 630,\n                alt: 'Infinite English - Nền Tảng Học Tiếng Anh với AI Cho Người Việt'\n            }\n        ]\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: 'Infinite English - Học Tiếng Anh Miễn Phí với AI | Nền Tảng Học Tiếng Anh Cho Người Việt',\n        description: 'Học tiếng Anh miễn phí với nền tảng AI thông minh dành cho người Việt. Câu hỏi trắc nghiệm vô hạn, bài học có cấu trúc, từ vựng và mẹo học hiệu quả.',\n        images: [\n            '/logo.png'\n        ],\n        creator: '@InfiniteEnglish'\n    },\n    alternates: {\n        canonical: '/',\n        languages: {\n            'vi-VN': '/',\n            'en-US': '/?lang=en',\n            'zh-CN': '/?lang=zh'\n        }\n    },\n    verification: {\n        google: 'your-google-verification-code',\n        yandex: 'your-yandex-verification-code',\n        yahoo: 'your-yahoo-verification-code'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"vi\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/svg+xml\",\n                        href: \"/logo.svg\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/logo.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#F97350\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"google-site-verification\",\n                        content: \"your-google-verification-code\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"canonical\",\n                        href: \"inenglish.io.vn\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"google-adsense-account\",\n                        content: \"ca-pub-****************\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"google-adsense-platform-account\",\n                        content: \"ca-pub-****************\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"ads-txt-verification\",\n                        content: \"ca-pub-****************\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"content-language\",\n                        content: \"vi,en\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"audience\",\n                        content: \"all\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"distribution\",\n                        content: \"global\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"rating\",\n                        content: \"general\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"robots\",\n                        content: \"index,follow,max-image-preview:large,max-snippet:-1,max-video-preview:-1\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"WebApplication\",\n                                \"name\": \"Infinite English\",\n                                \"description\": \"Ứng dụng học tiếng Anh miễn phí với AI tạo câu hỏi và bài tập vô hạn cho người Việt\",\n                                \"url\": \"https://inenglish.io.vn\",\n                                \"logo\": \"https://inenglish.io.vn/logo.png\",\n                                \"applicationCategory\": \"EducationalApplication\",\n                                \"operatingSystem\": \"Web Browser\",\n                                \"browserRequirements\": \"Requires JavaScript. Requires HTML5.\",\n                                \"softwareVersion\": \"1.0\",\n                                \"author\": {\n                                    \"@type\": \"Organization\",\n                                    \"name\": \"Infinite English Team\"\n                                },\n                                \"publisher\": {\n                                    \"@type\": \"Organization\",\n                                    \"name\": \"Infinite English\"\n                                },\n                                \"inLanguage\": [\n                                    \"vi-VN\",\n                                    \"en-US\"\n                                ],\n                                \"audience\": {\n                                    \"@type\": \"EducationalAudience\",\n                                    \"educationalRole\": \"student\"\n                                },\n                                \"educationalUse\": \"instruction\",\n                                \"learningResourceType\": [\n                                    \"exercise\",\n                                    \"quiz\",\n                                    \"lesson\",\n                                    \"practice\"\n                                ],\n                                \"educationalLevel\": [\n                                    \"beginner\",\n                                    \"intermediate\",\n                                    \"advanced\"\n                                ],\n                                \"about\": {\n                                    \"@type\": \"Thing\",\n                                    \"name\": \"English Language Learning\",\n                                    \"description\": \"Học tiếng Anh từ cơ bản đến nâng cao\"\n                                },\n                                \"featureList\": [\n                                    \"Câu hỏi trắc nghiệm vô hạn được tạo bởi AI\",\n                                    \"Bài học có cấu trúc theo trình độ\",\n                                    \"Luyện tập từ vựng và ngữ pháp\",\n                                    \"Hỗ trợ đa ngôn ngữ\",\n                                    \"Hoàn toàn miễn phí\"\n                                ],\n                                \"isAccessibleForFree\": true,\n                                \"license\": \"https://creativecommons.org/licenses/by-nc-sa/4.0/\"\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Providers__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Providers.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\AI\\webngonngu\\infinite-language\\src\\components\\Providers.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Providers.tsx */ \"(ssr)/./src/components/Providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CAI%5C%5Cwebngonngu%5C%5Cinfinite-language%5C%5Csrc%5C%5Ccomponents%5C%5CProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/PageTransition.tsx":
/*!*******************************************!*\
  !*** ./src/components/PageTransition.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PageTransition)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./src/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction PageTransition({ children }) {\n    const [isTransitioning, setIsTransitioning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [targetUrl, setTargetUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const previousPathnameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(pathname);\n    // Reset transition state when pathname changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PageTransition.useEffect\": ()=>{\n            if (previousPathnameRef.current !== pathname) {\n                setIsTransitioning(false);\n                setTargetUrl(null);\n                if (timeoutRef.current) {\n                    clearTimeout(timeoutRef.current);\n                    timeoutRef.current = null;\n                }\n                previousPathnameRef.current = pathname;\n            }\n        }\n    }[\"PageTransition.useEffect\"], [\n        pathname\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PageTransition.useEffect\": ()=>{\n            const handleRouteChange = {\n                \"PageTransition.useEffect.handleRouteChange\": ()=>{\n                    setIsTransitioning(false);\n                    setTargetUrl(null);\n                    if (timeoutRef.current) {\n                        clearTimeout(timeoutRef.current);\n                        timeoutRef.current = null;\n                    }\n                }\n            }[\"PageTransition.useEffect.handleRouteChange\"];\n            // Listen for route changes\n            window.addEventListener('popstate', handleRouteChange);\n            return ({\n                \"PageTransition.useEffect\": ()=>{\n                    window.removeEventListener('popstate', handleRouteChange);\n                    if (timeoutRef.current) {\n                        clearTimeout(timeoutRef.current);\n                    }\n                }\n            })[\"PageTransition.useEffect\"];\n        }\n    }[\"PageTransition.useEffect\"], []);\n    // Override link clicks to add transition\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PageTransition.useEffect\": ()=>{\n            const handleLinkClick = {\n                \"PageTransition.useEffect.handleLinkClick\": (e)=>{\n                    const target = e.target;\n                    const link = target.closest('a[href]');\n                    if (link && link.href && !link.href.startsWith('mailto:') && !link.href.startsWith('tel:')) {\n                        const url = new URL(link.href);\n                        const currentUrl = new URL(window.location.href);\n                        // Only intercept internal links and avoid same-page navigation\n                        if (url.origin === currentUrl.origin && !link.target && url.pathname + url.search !== currentUrl.pathname + currentUrl.search) {\n                            e.preventDefault();\n                            setTargetUrl(url.pathname + url.search);\n                            setIsTransitioning(true);\n                            // Clear any existing timeout\n                            if (timeoutRef.current) {\n                                clearTimeout(timeoutRef.current);\n                            }\n                            // Navigate after a short delay to show transition\n                            timeoutRef.current = setTimeout({\n                                \"PageTransition.useEffect.handleLinkClick\": ()=>{\n                                    router.push(url.pathname + url.search);\n                                    // Fallback: reset transition state after 5 seconds if navigation doesn't complete\n                                    timeoutRef.current = setTimeout({\n                                        \"PageTransition.useEffect.handleLinkClick\": ()=>{\n                                            setIsTransitioning(false);\n                                            setTargetUrl(null);\n                                        }\n                                    }[\"PageTransition.useEffect.handleLinkClick\"], 5000);\n                                }\n                            }[\"PageTransition.useEffect.handleLinkClick\"], 300);\n                        }\n                    }\n                }\n            }[\"PageTransition.useEffect.handleLinkClick\"];\n            document.addEventListener('click', handleLinkClick);\n            return ({\n                \"PageTransition.useEffect\": ()=>{\n                    document.removeEventListener('click', handleLinkClick);\n                }\n            })[\"PageTransition.useEffect\"];\n        }\n    }[\"PageTransition.useEffect\"], [\n        router\n    ]);\n    if (isTransitioning) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center relative overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-emerald-400/20 to-blue-600/20 rounded-full blur-3xl animate-pulse delay-1000\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-20 w-20 border-4 border-blue-200 mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-20 w-20 border-4 border-blue-500 border-t-transparent absolute top-0 left-1/2 transform -translate-x-1/2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center animate-pulse\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white text-lg\",\n                                            children: \"\\uD83D\\uDE80\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/80 backdrop-blur-xl rounded-2xl px-8 py-4 shadow-xl border border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-800 text-xl font-semibold mb-2\",\n                                    children: t('navigating') || 'Navigating...'\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-sm\",\n                                    children: targetUrl && `Going to ${targetUrl}`\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center space-x-2 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-blue-500 rounded-full animate-bounce\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-indigo-500 rounded-full animate-bounce delay-100\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-purple-500 rounded-full animate-bounce delay-200\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"transition-opacity duration-300 ease-in-out\",\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\PageTransition.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PageTransition.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Providers.tsx":
/*!**************************************!*\
  !*** ./src/components/Providers.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/LanguageContext */ \"(ssr)/./src/contexts/LanguageContext.tsx\");\n/* harmony import */ var _components_PageTransition__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/PageTransition */ \"(ssr)/./src/components/PageTransition.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Providers({ children }) {\n    // Allow disabling page transitions via environment variable or for debugging\n    const enableTransitions = process.env.NEXT_PUBLIC_ENABLE_PAGE_TRANSITIONS !== 'false';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.LanguageProvider, {\n                children: enableTransitions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PageTransition__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Providers.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 13\n                }, this) : children\n            }, void 0, false, {\n                fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Providers.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Providers.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\components\\\\Providers.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Providers.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const loading = status === 'loading';\n    const checkAuth = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"AuthProvider.useCallback[checkAuth]\": async ()=>{\n            const customSession = session;\n            if (customSession?.user) {\n                try {\n                    const response = await fetch('/api/auth/me');\n                    const data = await response.json();\n                    if (data.success) {\n                        setUser(data.user);\n                    } else {\n                        // If API fails, create user object from session\n                        setUser({\n                            id: customSession.user.id || '',\n                            email: customSession.user.email || '',\n                            username: customSession.user.username || customSession.user.name || '',\n                            preferredLanguage: 'en',\n                            stats: {\n                                totalQuestions: 0,\n                                correctAnswers: 0,\n                                streakCount: 0,\n                                lastActiveDate: new Date(),\n                                levelStats: {\n                                    beginner: {\n                                        total: 0,\n                                        correct: 0\n                                    },\n                                    intermediate: {\n                                        total: 0,\n                                        correct: 0\n                                    },\n                                    advanced: {\n                                        total: 0,\n                                        correct: 0\n                                    }\n                                }\n                            }\n                        });\n                    }\n                } catch (error) {\n                    console.error('Auth check error:', error);\n                    // Fallback to session data\n                    setUser({\n                        id: customSession.user.id || '',\n                        email: customSession.user.email || '',\n                        username: customSession.user.username || customSession.user.name || '',\n                        preferredLanguage: 'en',\n                        stats: {\n                            totalQuestions: 0,\n                            correctAnswers: 0,\n                            streakCount: 0,\n                            lastActiveDate: new Date(),\n                            levelStats: {\n                                beginner: {\n                                    total: 0,\n                                    correct: 0\n                                },\n                                intermediate: {\n                                    total: 0,\n                                    correct: 0\n                                },\n                                advanced: {\n                                    total: 0,\n                                    correct: 0\n                                }\n                            }\n                        }\n                    });\n                }\n            } else {\n                setUser(null);\n            }\n        }\n    }[\"AuthProvider.useCallback[checkAuth]\"], [\n        session\n    ]);\n    const logout = async ()=>{\n        try {\n            await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)({\n                redirect: false\n            });\n            setUser(null);\n        } catch (error) {\n            console.error('Logout error:', error);\n            setUser(null);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], [\n        checkAuth\n    ]);\n    const value = {\n        user,\n        logout,\n        loading,\n        checkAuth\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dHMvQXV0aENvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUUyRjtBQUNyQztBQUl0RCxNQUFNUSw0QkFBY1Asb0RBQWFBLENBQThCUTtBQUV4RCxTQUFTQyxhQUFhLEVBQUVDLFFBQVEsRUFBaUM7SUFDdEUsTUFBTSxFQUFFQyxNQUFNQyxPQUFPLEVBQUVDLE1BQU0sRUFBRSxHQUFHUiwyREFBVUE7SUFDNUMsTUFBTSxDQUFDUyxNQUFNQyxRQUFRLEdBQUdiLCtDQUFRQSxDQUFjO0lBQzlDLE1BQU1jLFVBQVVILFdBQVc7SUFFM0IsTUFBTUksWUFBWWIsa0RBQVdBOytDQUFDO1lBQzVCLE1BQU1jLGdCQUFnQk47WUFDdEIsSUFBSU0sZUFBZUosTUFBTTtnQkFDdkIsSUFBSTtvQkFDRixNQUFNSyxXQUFXLE1BQU1DLE1BQU07b0JBQzdCLE1BQU1ULE9BQU8sTUFBTVEsU0FBU0UsSUFBSTtvQkFFaEMsSUFBSVYsS0FBS1csT0FBTyxFQUFFO3dCQUNoQlAsUUFBUUosS0FBS0csSUFBSTtvQkFDbkIsT0FBTzt3QkFDTCxnREFBZ0Q7d0JBQ2hEQyxRQUFROzRCQUNOUSxJQUFJTCxjQUFjSixJQUFJLENBQUNTLEVBQUUsSUFBSTs0QkFDN0JDLE9BQU9OLGNBQWNKLElBQUksQ0FBQ1UsS0FBSyxJQUFJOzRCQUNuQ0MsVUFBVVAsY0FBY0osSUFBSSxDQUFDVyxRQUFRLElBQUlQLGNBQWNKLElBQUksQ0FBQ1ksSUFBSSxJQUFJOzRCQUNwRUMsbUJBQW1COzRCQUNuQkMsT0FBTztnQ0FDTEMsZ0JBQWdCO2dDQUNoQkMsZ0JBQWdCO2dDQUNoQkMsYUFBYTtnQ0FDYkMsZ0JBQWdCLElBQUlDO2dDQUNwQkMsWUFBWTtvQ0FDVkMsVUFBVTt3Q0FBRUMsT0FBTzt3Q0FBR0MsU0FBUztvQ0FBRTtvQ0FDakNDLGNBQWM7d0NBQUVGLE9BQU87d0NBQUdDLFNBQVM7b0NBQUU7b0NBQ3JDRSxVQUFVO3dDQUFFSCxPQUFPO3dDQUFHQyxTQUFTO29DQUFFO2dDQUNuQzs0QkFDRjt3QkFDRjtvQkFDRjtnQkFDRixFQUFFLE9BQU9HLE9BQU87b0JBQ2RDLFFBQVFELEtBQUssQ0FBQyxxQkFBcUJBO29CQUNuQywyQkFBMkI7b0JBQzNCekIsUUFBUTt3QkFDTlEsSUFBSUwsY0FBY0osSUFBSSxDQUFDUyxFQUFFLElBQUk7d0JBQzdCQyxPQUFPTixjQUFjSixJQUFJLENBQUNVLEtBQUssSUFBSTt3QkFDbkNDLFVBQVVQLGNBQWNKLElBQUksQ0FBQ1csUUFBUSxJQUFJUCxjQUFjSixJQUFJLENBQUNZLElBQUksSUFBSTt3QkFDcEVDLG1CQUFtQjt3QkFDbkJDLE9BQU87NEJBQ0xDLGdCQUFnQjs0QkFDaEJDLGdCQUFnQjs0QkFDaEJDLGFBQWE7NEJBQ2JDLGdCQUFnQixJQUFJQzs0QkFDcEJDLFlBQVk7Z0NBQ1ZDLFVBQVU7b0NBQUVDLE9BQU87b0NBQUdDLFNBQVM7Z0NBQUU7Z0NBQ2pDQyxjQUFjO29DQUFFRixPQUFPO29DQUFHQyxTQUFTO2dDQUFFO2dDQUNyQ0UsVUFBVTtvQ0FBRUgsT0FBTztvQ0FBR0MsU0FBUztnQ0FBRTs0QkFDbkM7d0JBQ0Y7b0JBQ0Y7Z0JBQ0Y7WUFDRixPQUFPO2dCQUNMdEIsUUFBUTtZQUNWO1FBQ0Y7OENBQUc7UUFBQ0g7S0FBUTtJQUVaLE1BQU04QixTQUFTO1FBQ2IsSUFBSTtZQUNGLE1BQU1wQyx3REFBT0EsQ0FBQztnQkFBRXFDLFVBQVU7WUFBTTtZQUNoQzVCLFFBQVE7UUFDVixFQUFFLE9BQU95QixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxpQkFBaUJBO1lBQy9CekIsUUFBUTtRQUNWO0lBQ0Y7SUFFQVosZ0RBQVNBO2tDQUFDO1lBQ1JjO1FBQ0Y7aUNBQUc7UUFBQ0E7S0FBVTtJQUVkLE1BQU0yQixRQUF5QjtRQUM3QjlCO1FBQ0E0QjtRQUNBMUI7UUFDQUM7SUFDRjtJQUVBLHFCQUNFLDhEQUFDVixZQUFZc0MsUUFBUTtRQUFDRCxPQUFPQTtrQkFDMUJsQzs7Ozs7O0FBR1A7QUFFTyxTQUFTb0M7SUFDZCxNQUFNQyxVQUFVOUMsaURBQVVBLENBQUNNO0lBQzNCLElBQUl3QyxZQUFZdkMsV0FBVztRQUN6QixNQUFNLElBQUl3QyxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVCIsInNvdXJjZXMiOlsiRDpcXEFJXFx3ZWJuZ29ubmd1XFxpbmZpbml0ZS1sYW5ndWFnZVxcc3JjXFxjb250ZXh0c1xcQXV0aENvbnRleHQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlU2Vzc2lvbiwgc2lnbk91dCB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XG5pbXBvcnQgeyBVc2VyLCBBdXRoQ29udGV4dFR5cGUgfSBmcm9tICdAL3R5cGVzJztcbmltcG9ydCB0eXBlIHsgU2Vzc2lvbiB9IGZyb20gJ25leHQtYXV0aCc7XG5cbmNvbnN0IEF1dGhDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxBdXRoQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XG5cbmV4cG9ydCBmdW5jdGlvbiBBdXRoUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICBjb25zdCB7IGRhdGE6IHNlc3Npb24sIHN0YXR1cyB9ID0gdXNlU2Vzc2lvbigpO1xuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxVc2VyIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IGxvYWRpbmcgPSBzdGF0dXMgPT09ICdsb2FkaW5nJztcblxuICBjb25zdCBjaGVja0F1dGggPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgY29uc3QgY3VzdG9tU2Vzc2lvbiA9IHNlc3Npb24gYXMgU2Vzc2lvbiB8IG51bGw7XG4gICAgaWYgKGN1c3RvbVNlc3Npb24/LnVzZXIpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXV0aC9tZScpO1xuICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICAgIGlmIChkYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgICBzZXRVc2VyKGRhdGEudXNlcik7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgLy8gSWYgQVBJIGZhaWxzLCBjcmVhdGUgdXNlciBvYmplY3QgZnJvbSBzZXNzaW9uXG4gICAgICAgICAgc2V0VXNlcih7XG4gICAgICAgICAgICBpZDogY3VzdG9tU2Vzc2lvbi51c2VyLmlkIHx8ICcnLFxuICAgICAgICAgICAgZW1haWw6IGN1c3RvbVNlc3Npb24udXNlci5lbWFpbCB8fCAnJyxcbiAgICAgICAgICAgIHVzZXJuYW1lOiBjdXN0b21TZXNzaW9uLnVzZXIudXNlcm5hbWUgfHwgY3VzdG9tU2Vzc2lvbi51c2VyLm5hbWUgfHwgJycsXG4gICAgICAgICAgICBwcmVmZXJyZWRMYW5ndWFnZTogJ2VuJyxcbiAgICAgICAgICAgIHN0YXRzOiB7XG4gICAgICAgICAgICAgIHRvdGFsUXVlc3Rpb25zOiAwLFxuICAgICAgICAgICAgICBjb3JyZWN0QW5zd2VyczogMCxcbiAgICAgICAgICAgICAgc3RyZWFrQ291bnQ6IDAsXG4gICAgICAgICAgICAgIGxhc3RBY3RpdmVEYXRlOiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgICBsZXZlbFN0YXRzOiB7XG4gICAgICAgICAgICAgICAgYmVnaW5uZXI6IHsgdG90YWw6IDAsIGNvcnJlY3Q6IDAgfSxcbiAgICAgICAgICAgICAgICBpbnRlcm1lZGlhdGU6IHsgdG90YWw6IDAsIGNvcnJlY3Q6IDAgfSxcbiAgICAgICAgICAgICAgICBhZHZhbmNlZDogeyB0b3RhbDogMCwgY29ycmVjdDogMCB9LFxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGggY2hlY2sgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgICAvLyBGYWxsYmFjayB0byBzZXNzaW9uIGRhdGFcbiAgICAgICAgc2V0VXNlcih7XG4gICAgICAgICAgaWQ6IGN1c3RvbVNlc3Npb24udXNlci5pZCB8fCAnJyxcbiAgICAgICAgICBlbWFpbDogY3VzdG9tU2Vzc2lvbi51c2VyLmVtYWlsIHx8ICcnLFxuICAgICAgICAgIHVzZXJuYW1lOiBjdXN0b21TZXNzaW9uLnVzZXIudXNlcm5hbWUgfHwgY3VzdG9tU2Vzc2lvbi51c2VyLm5hbWUgfHwgJycsXG4gICAgICAgICAgcHJlZmVycmVkTGFuZ3VhZ2U6ICdlbicsXG4gICAgICAgICAgc3RhdHM6IHtcbiAgICAgICAgICAgIHRvdGFsUXVlc3Rpb25zOiAwLFxuICAgICAgICAgICAgY29ycmVjdEFuc3dlcnM6IDAsXG4gICAgICAgICAgICBzdHJlYWtDb3VudDogMCxcbiAgICAgICAgICAgIGxhc3RBY3RpdmVEYXRlOiBuZXcgRGF0ZSgpLFxuICAgICAgICAgICAgbGV2ZWxTdGF0czoge1xuICAgICAgICAgICAgICBiZWdpbm5lcjogeyB0b3RhbDogMCwgY29ycmVjdDogMCB9LFxuICAgICAgICAgICAgICBpbnRlcm1lZGlhdGU6IHsgdG90YWw6IDAsIGNvcnJlY3Q6IDAgfSxcbiAgICAgICAgICAgICAgYWR2YW5jZWQ6IHsgdG90YWw6IDAsIGNvcnJlY3Q6IDAgfSxcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICBzZXRVc2VyKG51bGwpO1xuICAgIH1cbiAgfSwgW3Nlc3Npb25dKTtcblxuICBjb25zdCBsb2dvdXQgPSBhc3luYyAoKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHNpZ25PdXQoeyByZWRpcmVjdDogZmFsc2UgfSk7XG4gICAgICBzZXRVc2VyKG51bGwpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdMb2dvdXQgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgc2V0VXNlcihudWxsKTtcbiAgICB9XG4gIH07XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjaGVja0F1dGgoKTtcbiAgfSwgW2NoZWNrQXV0aF0pO1xuXG4gIGNvbnN0IHZhbHVlOiBBdXRoQ29udGV4dFR5cGUgPSB7XG4gICAgdXNlcixcbiAgICBsb2dvdXQsXG4gICAgbG9hZGluZyxcbiAgICBjaGVja0F1dGgsXG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8QXV0aENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0F1dGhDb250ZXh0LlByb3ZpZGVyPlxuICApO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXV0aCgpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoQXV0aENvbnRleHQpO1xuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VBdXRoIG11c3QgYmUgdXNlZCB3aXRoaW4gYW4gQXV0aFByb3ZpZGVyJyk7XG4gIH1cbiAgcmV0dXJuIGNvbnRleHQ7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJ1c2VTZXNzaW9uIiwic2lnbk91dCIsIkF1dGhDb250ZXh0IiwidW5kZWZpbmVkIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iLCJkYXRhIiwic2Vzc2lvbiIsInN0YXR1cyIsInVzZXIiLCJzZXRVc2VyIiwibG9hZGluZyIsImNoZWNrQXV0aCIsImN1c3RvbVNlc3Npb24iLCJyZXNwb25zZSIsImZldGNoIiwianNvbiIsInN1Y2Nlc3MiLCJpZCIsImVtYWlsIiwidXNlcm5hbWUiLCJuYW1lIiwicHJlZmVycmVkTGFuZ3VhZ2UiLCJzdGF0cyIsInRvdGFsUXVlc3Rpb25zIiwiY29ycmVjdEFuc3dlcnMiLCJzdHJlYWtDb3VudCIsImxhc3RBY3RpdmVEYXRlIiwiRGF0ZSIsImxldmVsU3RhdHMiLCJiZWdpbm5lciIsInRvdGFsIiwiY29ycmVjdCIsImludGVybWVkaWF0ZSIsImFkdmFuY2VkIiwiZXJyb3IiLCJjb25zb2xlIiwibG9nb3V0IiwicmVkaXJlY3QiLCJ2YWx1ZSIsIlByb3ZpZGVyIiwidXNlQXV0aCIsImNvbnRleHQiLCJFcnJvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/LanguageContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/LanguageContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _i18n_translations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/i18n/translations */ \"(ssr)/./src/i18n/translations.ts\");\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \n\n\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction LanguageProvider({ children }) {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('vi');\n    // Load language from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            const savedLanguage = localStorage.getItem('language');\n            if (savedLanguage && [\n                'en',\n                'vi',\n                'zh'\n            ].includes(savedLanguage)) {\n                setLanguage(savedLanguage);\n            }\n        }\n    }[\"LanguageProvider.useEffect\"], []);\n    // Save language to localStorage when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            localStorage.setItem('language', language);\n        }\n    }[\"LanguageProvider.useEffect\"], [\n        language\n    ]);\n    const t = (key)=>{\n        const keys = key.split('.');\n        let value = _i18n_translations__WEBPACK_IMPORTED_MODULE_2__.translations[language];\n        for (const k of keys){\n            if (value && typeof value === 'object' && k in value) {\n                value = value[k];\n            } else {\n                return key;\n            }\n        }\n        return typeof value === 'string' ? value : key;\n    };\n    const value = {\n        language,\n        setLanguage,\n        t\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\AI\\\\webngonngu\\\\infinite-language\\\\src\\\\contexts\\\\LanguageContext.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\nfunction useLanguage() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error('useLanguage must be used within a LanguageProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/LanguageContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/i18n/translations.ts":
/*!**********************************!*\
  !*** ./src/i18n/translations.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   translations: () => (/* binding */ translations)\n/* harmony export */ });\nconst translations = {\n    en: {\n        //Login\n        welcomeMessage: \"Sign in to track your progress and continue learning\",\n        // App Title\n        appTitle: \"Infinite English\",\n        appSubtitle: \"Learn English with AI-powered lessons and quizzes\",\n        // Level Selection\n        beginner: \"Beginner\",\n        intermediate: \"Intermediate\",\n        advanced: \"Advanced\",\n        beginnerDesc: \"Basic vocabulary and simple grammar\",\n        intermediateDesc: \"More complex sentences and vocabulary\",\n        advancedDesc: \"Complex grammar and advanced vocabulary\",\n        // Navigation\n        backToLevels: \"← Back to Levels\",\n        nextQuestion: \"Next Question\",\n        // Quiz\n        score: \"Score\",\n        correct: \"Correct!\",\n        incorrect: \"Incorrect!\",\n        question: \"Question:\",\n        yourAnswer: \"Your answer:\",\n        correctAnswer: \"Correct answer:\",\n        explanation: \"Explanation:\",\n        answers: \"Answers\",\n        total: \"Total\",\n        selectAnswer: \"Select your answer\",\n        answerSelected: \"Answer selected! Checking...\",\n        // Loading\n        generatingQuestion: \"Generating your question...\",\n        creatingQuestion: \"Creating a personalized question just for you\",\n        navigating: \"Navigating...\",\n        loading: \"Loading...\",\n        // Score\n        resetScore: \"Reset Score\",\n        // Language Switcher\n        language: \"Language\",\n        english: \"English\",\n        vietnamese: \"Tiếng Việt\",\n        chinese: \"中文\",\n        // Features Section\n        whyChooseTitle: \"Why Choose Infinite English?\",\n        whyChooseSubtitle: \"Experience the future of language learning with our cutting-edge features\",\n        aiGeneratedTitle: \"Unlimited Questions\",\n        aiGeneratedDesc: \"Endless unique questions with personalized learning experience tailored to your level\",\n        multiLanguageTitle: \"Multi-Language Support\",\n        multiLanguageDesc: \"Learn in Vietnamese, Chinese, or English with seamless language switching\",\n        progressTrackingTitle: \"Progress Tracking\",\n        progressTrackingDesc: \"Monitor your learning journey with detailed statistics and performance analytics\",\n        // Stats\n        questionsLabel: \"Questions\",\n        languagesLabel: \"Languages\",\n        aiPoweredLabel: \"Smart Learning\",\n        // Authentication\n        login: \"Login\",\n        register: \"Register\",\n        logout: \"Logout\",\n        email: \"Email\",\n        username: \"Username\",\n        password: \"Password\",\n        confirmPassword: \"Confirm Password\",\n        needAccount: \"Don't have an account? Register\",\n        haveAccount: \"Already have an account? Login\",\n        loginRequired: \"Login Required\",\n        loginToViewHistory: \"Please login to view your quiz history\",\n        // User Stats\n        stats: \"Statistics\",\n        totalQuestions: \"Total Questions\",\n        accuracy: \"Accuracy\",\n        streak: \"Streak\",\n        viewHistory: \"View History\",\n        // History\n        quizHistory: \"Quiz History\",\n        backToQuiz: \"Back to Quiz\",\n        allLevels: \"All Levels\",\n        allResults: \"All Results\",\n        result: \"Result\",\n        noHistory: \"No quiz history found\",\n        previous: \"Previous\",\n        next: \"Next\",\n        // AI Features\n        aiLessons: \"AI Lessons\",\n        aiReading: \"AI Reading\",\n        aiTutor: \"AI Tutor\",\n        createLesson: \"Create Lesson\",\n        generateReading: \"Generate Reading\",\n        askTutor: \"Ask AI Tutor\",\n        // Lessons\n        lessonTitle: \"AI English Lessons\",\n        lessonSubtitle: \"Create personalized English lessons with AI. Choose your topic, level, and lesson type to generate comprehensive learning content.\",\n        lessonType: \"Lesson Type\",\n        duration: \"Duration\",\n        topic: \"Topic\",\n        topicPlaceholder: \"e.g., Present Simple Tense, Business English, Travel Vocabulary...\",\n        topicSuggestions: \"Or choose from popular topics:\",\n        generateLesson: \"Generate Lesson\",\n        generating: \"Generating...\",\n        // Reading\n        readingTitle: \"AI Reading Practice\",\n        readingSubtitle: \"Generate personalized reading passages with AI. Choose your level, topic, and style to create unlimited practice content.\",\n        wordCount: \"Word Count\",\n        style: \"Style\",\n        generatePassage: \"Generate Passage\",\n        vocabulary: \"Vocabulary\",\n        comprehensionQuestions: \"Comprehension Questions\",\n        submitAnswers: \"Submit Answers\",\n        tryAgain: \"Try Again\",\n        generateNew: \"Generate New Passage\",\n        // Dashboard\n        dashboardTitle: \"Learning Dashboard\",\n        dashboardSubtitle: \"Track your progress, analyze your performance, and get personalized recommendations to improve your English.\",\n        quizzesTaken: \"Quizzes Taken\",\n        overallPerformance: \"Overall Performance\",\n        streakDays: \"Day Streak\",\n        studyTime: \"Study Time\",\n        progressByLevel: \"Progress by Level\",\n        aiAnalysis: \"AI Performance Analysis\",\n        strengths: \"Strengths\",\n        improvements: \"Areas for Improvement\",\n        nextSteps: \"Next Steps\",\n        motivationalMessage: \"Motivational Message\",\n        continuelearning: \"Continue Learning\",\n        // Tutor\n        tutorTitle: \"AI English Tutor\",\n        tutorSubtitle: \"Get instant help with grammar, vocabulary, pronunciation, and more. Ask questions in your native language.\",\n        askQuestion: \"Ask a question...\",\n        send: \"Send\",\n        grammarHelp: \"Grammar Help\",\n        vocabularyHelp: \"Vocabulary\",\n        pronunciationHelp: \"Pronunciation\",\n        writingHelp: \"Writing Help\",\n        conversationHelp: \"Conversation\",\n        cultureHelp: \"Culture\",\n        // Footer\n        footerDescription: \"Learn English for free with our AI-powered platform designed for Vietnamese learners. Unlimited multiple choice questions, structured lessons, vocabulary and effective learning tips. Suitable for all levels from basic to advanced.\",\n        contactAdvertising: \"Contact for Advertising\",\n        quickLinks: \"Quick Links\",\n        about: \"About\",\n        contact: \"Contact\",\n        legal: \"Legal\",\n        terms: \"Terms of Service\",\n        privacy: \"Privacy Policy\",\n        allRightsReserved: \"All rights reserved.\",\n        disclaimer: \"Disclaimer\",\n        disclaimerText: \"Our website only provides online English learning services for entertainment and content sharing purposes. All learning content posted on the website is collected from various sources on the internet and we are not responsible for copyright or ownership of any content. If you are a copyright owner and believe that content on the site violates your rights, please contact us to remove the infringing content promptly. In addition, we are not responsible for advertising content displayed on the website, including but not limited to advertising products or services of third parties. These advertisements do not reflect our views or commitments. Users need to consider and take responsibility when interacting with such advertisements.\",\n        // Common\n        level: \"Level\",\n        back: \"Back\",\n        continue: \"Continue\",\n        complete: \"Complete\",\n        start: \"Start\",\n        finish: \"Finish\",\n        save: \"Save\",\n        cancel: \"Cancel\",\n        close: \"Close\",\n        signIn: \"Sign In\",\n        signOut: \"Sign Out\",\n        signUp: \"Sign Up\",\n        // Auth Modal\n        continueWithGoogle: \"Continue with Google\",\n        continueWithFacebook: \"Continue with Facebook\",\n        optionalLoginNote: \"Login is optional. You can continue without an account, but your progress won't be saved.\",\n        loginError: \"Failed to login with {provider}. Please try again.\",\n        // Language Switcher\n        active: \"Active\",\n        // User Menu\n        learningDashboard: \"Learning Dashboard\",\n        accuracyText: \"accuracy\",\n        // Error Messages\n        aiErrorMessage: \"Sorry, I encountered an error while processing your question. Please try again.\",\n        failedToGenerate: \"Failed to Generate Question\",\n        questionGenerateError: \"Sorry, we couldn't generate a question at this time. Please try again later.\",\n        tryAgainLater: \"Try Again Later\",\n        backToHome: \"Back to Home\",\n        // Loading States\n        processing: \"Processing...\",\n        pleaseWait: \"Please wait...\",\n        loadingHistory: \"Loading your quiz history...\",\n        // General Error States\n        somethingWentWrong: \"Something went wrong\",\n        errorOccurred: \"An error occurred\",\n        retryAction: \"Retry\",\n        // Additional Reading Component\n        aiGenerates: \"AI Generates\",\n        aiGeneratesDesc: \"Our AI creates a unique passage with questions\",\n        failedToGeneratePassage: \"Failed to generate reading passage. Please try again.\",\n        // Additional Lessons Component\n        aiCreates: \"AI Creates\",\n        aiCreatesDesc: \"AI generates comprehensive lesson content\",\n        failedToGenerateLesson: \"Failed to generate lesson. Please try again.\",\n        // History Page\n        noHistoryYet: \"No History Yet\",\n        // Question Card\n        processingAnswer: \"Processing your answer...\",\n        chooseAnswer: \"Choose your answer\"\n    },\n    vi: {\n        //Login\n        welcomeMessage: \"Đăng nhập để theo dõi tiến độ và tiếp tục học\",\n        // App Title\n        appTitle: \"Infinite English\",\n        appSubtitle: \"Học tiếng Anh với bài học và quiz AI\",\n        // Level Selection\n        beginner: \"Cơ bản\",\n        intermediate: \"Trung bình\",\n        advanced: \"Nâng cao\",\n        beginnerDesc: \"Từ vựng cơ bản và ngữ pháp đơn giản\",\n        intermediateDesc: \"Câu và từ vựng phức tạp hơn\",\n        advancedDesc: \"Ngữ pháp phức tạp và từ vựng nâng cao\",\n        // Navigation\n        backToLevels: \"Quay lại Cấp độ\",\n        nextQuestion: \"Câu hỏi tiếp theo\",\n        // Quiz\n        score: \"Điểm\",\n        correct: \"Đúng rồi!\",\n        incorrect: \"Sai rồi!\",\n        question: \"Câu hỏi:\",\n        yourAnswer: \"Câu trả lời của bạn:\",\n        correctAnswer: \"Đáp án đúng:\",\n        explanation: \"Giải thích:\",\n        answers: \"Câu trả lời\",\n        total: \"Tổng\",\n        selectAnswer: \"Chọn câu trả lời của bạn\",\n        answerSelected: \"Đã chọn câu trả lời! Đang kiểm tra...\",\n        // Loading\n        generatingQuestion: \"Đang tạo câu hỏi cho bạn...\",\n        creatingQuestion: \"Đang tạo câu hỏi cá nhân hóa dành riêng cho bạn\",\n        navigating: \"Đang chuyển trang...\",\n        loading: \"Đang tải...\",\n        // Score\n        resetScore: \"Đặt lại điểm\",\n        // Language Switcher\n        language: \"Ngôn ngữ\",\n        english: \"English\",\n        vietnamese: \"Tiếng Việt\",\n        chinese: \"中文\",\n        // Features Section\n        whyChooseTitle: \"Tại sao chọn Infinite English?\",\n        whyChooseSubtitle: \"Trải nghiệm tương lai của việc học ngôn ngữ với các tính năng tiên tiến\",\n        aiGeneratedTitle: \"Câu hỏi vô hạn\",\n        aiGeneratedDesc: \"Câu hỏi độc đáo không giới hạn với trải nghiệm học tập cá nhân hóa phù hợp với trình độ của bạn\",\n        multiLanguageTitle: \"Hỗ trợ đa ngôn ngữ\",\n        multiLanguageDesc: \"Học bằng tiếng Việt, tiếng Trung hoặc tiếng Anh với khả năng chuyển đổi ngôn ngữ liền mạch\",\n        progressTrackingTitle: \"Theo dõi tiến độ\",\n        progressTrackingDesc: \"Theo dõi hành trình học tập của bạn với thống kê chi tiết và phân tích hiệu suất\",\n        // Stats\n        questionsLabel: \"Câu hỏi\",\n        languagesLabel: \"Ngôn ngữ\",\n        aiPoweredLabel: \"Học thông minh\",\n        // Authentication\n        login: \"Đăng nhập\",\n        register: \"Đăng ký\",\n        logout: \"Đăng xuất\",\n        email: \"Email\",\n        username: \"Tên người dùng\",\n        password: \"Mật khẩu\",\n        confirmPassword: \"Xác nhận mật khẩu\",\n        needAccount: \"Chưa có tài khoản? Đăng ký\",\n        haveAccount: \"Đã có tài khoản? Đăng nhập\",\n        loginRequired: \"Yêu cầu đăng nhập\",\n        loginToViewHistory: \"Vui lòng đăng nhập để xem lịch sử quiz\",\n        // User Stats\n        stats: \"Thống kê\",\n        totalQuestions: \"Tổng câu hỏi\",\n        accuracy: \"Độ chính xác\",\n        streak: \"Chuỗi đúng\",\n        viewHistory: \"Xem lịch sử\",\n        // History\n        quizHistory: \"Lịch sử Quiz\",\n        backToQuiz: \"Quay lại Quiz\",\n        allLevels: \"Tất cả cấp độ\",\n        allResults: \"Tất cả kết quả\",\n        result: \"Kết quả\",\n        noHistory: \"Không tìm thấy lịch sử quiz\",\n        previous: \"Trước\",\n        next: \"Tiếp\",\n        // AI Features\n        aiLessons: \"Bài học AI\",\n        aiReading: \"Đọc hiểu AI\",\n        aiTutor: \"Gia sư AI\",\n        createLesson: \"Tạo bài học\",\n        generateReading: \"Tạo bài đọc\",\n        askTutor: \"Hỏi gia sư AI\",\n        // Lessons\n        lessonTitle: \"Bài học tiếng Anh AI\",\n        lessonSubtitle: \"Tạo bài học tiếng Anh cá nhân hóa với AI. Chọn chủ đề, cấp độ và loại bài học để tạo nội dung học tập toàn diện.\",\n        lessonType: \"Loại bài học\",\n        duration: \"Thời lượng\",\n        topic: \"Chủ đề\",\n        topicPlaceholder: \"ví dụ: Thì hiện tại đơn, Tiếng Anh thương mại, Từ vựng du lịch...\",\n        topicSuggestions: \"Hoặc chọn từ các chủ đề phổ biến:\",\n        generateLesson: \"Tạo bài học\",\n        generating: \"Đang tạo...\",\n        // Reading\n        readingTitle: \"Luyện đọc hiểu AI\",\n        readingSubtitle: \"Tạo đoạn văn đọc hiểu cá nhân hóa với AI. Chọn cấp độ, chủ đề và phong cách để tạo nội dung luyện tập không giới hạn.\",\n        wordCount: \"Số từ\",\n        style: \"Phong cách\",\n        generatePassage: \"Tạo đoạn văn\",\n        vocabulary: \"Từ vựng\",\n        comprehensionQuestions: \"Câu hỏi đọc hiểu\",\n        submitAnswers: \"Nộp bài\",\n        tryAgain: \"Thử lại\",\n        generateNew: \"Tạo đoạn văn mới\",\n        // Dashboard\n        dashboardTitle: \"Bảng điều khiển học tập\",\n        dashboardSubtitle: \"Theo dõi tiến độ, phân tích hiệu suất và nhận gợi ý cá nhân hóa để cải thiện tiếng Anh.\",\n        quizzesTaken: \"Bài quiz đã làm\",\n        overallPerformance: \"Hiệu suất tổng thể\",\n        streakDays: \"Chuỗi ngày\",\n        studyTime: \"Thời gian học\",\n        progressByLevel: \"Tiến độ theo cấp độ\",\n        aiAnalysis: \"Phân tích hiệu suất AI\",\n        strengths: \"Điểm mạnh\",\n        improvements: \"Cần cải thiện\",\n        nextSteps: \"Bước tiếp theo\",\n        motivationalMessage: \"Thông điệp động viên\",\n        continuelearning: \"Tiếp tục học\",\n        // Tutor\n        tutorTitle: \"Gia sư tiếng Anh AI\",\n        tutorSubtitle: \"Nhận trợ giúp tức thì về ngữ pháp, từ vựng, phát âm và nhiều hơn nữa. Đặt câu hỏi bằng tiếng mẹ đẻ.\",\n        askQuestion: \"Đặt câu hỏi...\",\n        send: \"Gửi\",\n        grammarHelp: \"Trợ giúp ngữ pháp\",\n        vocabularyHelp: \"Từ vựng\",\n        pronunciationHelp: \"Phát âm\",\n        writingHelp: \"Trợ giúp viết\",\n        conversationHelp: \"Hội thoại\",\n        cultureHelp: \"Văn hóa\",\n        // Footer\n        footerDescription: \"Học tiếng Anh miễn phí với nền tảng AI thông minh dành cho người Việt. Câu hỏi trắc nghiệm vô hạn, bài học có cấu trúc, từ vựng và mẹo học hiệu quả. Phù hợp mọi trình độ từ cơ bản đến nâng cao.\",\n        contactAdvertising: \"Liên hệ đặt quảng cáo\",\n        quickLinks: \"Liên kết nhanh\",\n        about: \"Giới thiệu\",\n        contact: \"Liên hệ\",\n        legal: \"Pháp lý\",\n        terms: \"Điều khoản\",\n        privacy: \"Chính sách bảo mật\",\n        allRightsReserved: \"Tất cả quyền được bảo lưu.\",\n        disclaimer: \"Miễn trừ trách nhiệm\",\n        disclaimerText: \"Trang web của chúng tôi chỉ cung cấp dịch vụ học tiếng Anh online với mục đích giải trí và chia sẻ nội dung. Toàn bộ nội dung học tập được đăng tải trên trang web được sưu tầm từ nhiều nguồn trên internet và chúng tôi không chịu trách nhiệm về bản quyền hoặc quyền sở hữu đối với bất kỳ nội dung nào. Nếu bạn là chủ sở hữu bản quyền và cho rằng nội dung trên trang vi phạm quyền của bạn, vui lòng liên hệ với chúng tôi để tiến hành gỡ bỏ nội dung vi phạm một cách kịp thời. Ngoài ra, chúng tôi không chịu trách nhiệm về các nội dung quảng cáo hiển thị trên trang web, bao gồm nhưng không giới hạn ở việc quảng cáo sản phẩm hoặc dịch vụ của bên thứ ba. Những quảng cáo này không phản ánh quan điểm hoặc cam kết của chúng tôi. Người dùng cần tự cân nhắc và chịu trách nhiệm khi tương tác với các quảng cáo đó.\",\n        // Common\n        level: \"Cấp độ\",\n        back: \"Quay lại\",\n        continue: \"Tiếp tục\",\n        complete: \"Hoàn thành\",\n        start: \"Bắt đầu\",\n        finish: \"Kết thúc\",\n        save: \"Lưu\",\n        cancel: \"Hủy\",\n        close: \"Đóng\",\n        signIn: \"Đăng nhập\",\n        signOut: \"Đăng xuất\",\n        signUp: \"Đăng ký\",\n        // Auth Modal\n        continueWithGoogle: \"Tiếp tục với Google\",\n        continueWithFacebook: \"Tiếp tục với Facebook\",\n        optionalLoginNote: \"Đăng nhập là tùy chọn. Bạn có thể tiếp tục mà không cần tài khoản, nhưng tiến độ sẽ không được lưu.\",\n        loginError: \"Đăng nhập với {provider} thất bại. Vui lòng thử lại.\",\n        // Language Switcher\n        active: \"Đang hoạt động\",\n        // User Menu\n        learningDashboard: \"Bảng điều khiển học tập\",\n        accuracyText: \"độ chính xác\",\n        // Error Messages\n        aiErrorMessage: \"Xin lỗi, tôi gặp lỗi khi xử lý câu hỏi của bạn. Vui lòng thử lại.\",\n        failedToGenerate: \"Không thể tạo câu hỏi\",\n        questionGenerateError: \"Xin lỗi, chúng tôi không thể tạo câu hỏi vào lúc này. Vui lòng thử lại sau.\",\n        tryAgainLater: \"Thử lại sau\",\n        backToHome: \"Về trang chủ\",\n        // Loading States\n        processing: \"Đang xử lý...\",\n        pleaseWait: \"Vui lòng đợi...\",\n        loadingHistory: \"Đang tải lịch sử quiz của bạn...\",\n        // General Error States\n        somethingWentWrong: \"Đã xảy ra lỗi\",\n        errorOccurred: \"Đã xảy ra lỗi\",\n        retryAction: \"Thử lại\",\n        // Additional Reading Component\n        aiGenerates: \"AI Tạo nội dung\",\n        aiGeneratesDesc: \"AI của chúng tôi tạo ra đoạn văn độc đáo với các câu hỏi\",\n        failedToGeneratePassage: \"Không thể tạo đoạn văn đọc hiểu. Vui lòng thử lại.\",\n        // Additional Lessons Component\n        aiCreates: \"AI Tạo bài học\",\n        aiCreatesDesc: \"AI tạo ra nội dung bài học toàn diện\",\n        failedToGenerateLesson: \"Không thể tạo bài học. Vui lòng thử lại.\",\n        // History Page\n        noHistoryYet: \"Chưa có lịch sử\",\n        // Question Card\n        processingAnswer: \"Đang xử lý câu trả lời của bạn...\",\n        chooseAnswer: \"Chọn câu trả lời của bạn\"\n    },\n    zh: {\n        //Login\n        welcomeMessage: \"登录以跟踪您的进度并继续学习\",\n        // App Title\n        appTitle: \"Infinite English\",\n        appSubtitle: \"用AI课程和测验学英语\",\n        // Level Selection\n        beginner: \"初级\",\n        intermediate: \"中级\",\n        advanced: \"高级\",\n        beginnerDesc: \"基础词汇和简单语法\",\n        intermediateDesc: \"更复杂的句子和词汇\",\n        advancedDesc: \"复杂语法和高级词汇\",\n        // Navigation\n        backToLevels: \"← 返回级别\",\n        nextQuestion: \"下一题\",\n        // Quiz\n        score: \"分数\",\n        correct: \"正确！\",\n        incorrect: \"错误！\",\n        question: \"问题：\",\n        yourAnswer: \"您的答案：\",\n        correctAnswer: \"正确答案：\",\n        explanation: \"解释：\",\n        answers: \"答案\",\n        total: \"总计\",\n        selectAnswer: \"选择您的答案\",\n        answerSelected: \"已选择答案！正在检查...\",\n        // Loading\n        generatingQuestion: \"正在为您生成问题...\",\n        creatingQuestion: \"正在为您创建个性化问题\",\n        navigating: \"正在导航...\",\n        loading: \"加载中...\",\n        // Score\n        resetScore: \"重置分数\",\n        // Language Switcher\n        language: \"语言\",\n        english: \"English\",\n        vietnamese: \"Tiếng Việt\",\n        chinese: \"中文\",\n        // Features Section\n        whyChooseTitle: \"为什么选择 Infinite English？\",\n        whyChooseSubtitle: \"体验我们尖端功能带来的语言学习未来\",\n        aiGeneratedTitle: \"无限题库\",\n        aiGeneratedDesc: \"无限独特问题，提供适合您水平的个性化学习体验\",\n        multiLanguageTitle: \"多语言支持\",\n        multiLanguageDesc: \"支持越南语、中文或英语学习，语言切换无缝衔接\",\n        progressTrackingTitle: \"进度跟踪\",\n        progressTrackingDesc: \"通过详细统计和性能分析监控您的学习历程\",\n        // Stats\n        questionsLabel: \"问题\",\n        languagesLabel: \"语言\",\n        aiPoweredLabel: \"智能学习\",\n        // Authentication\n        login: \"登录\",\n        register: \"注册\",\n        logout: \"退出\",\n        email: \"邮箱\",\n        username: \"用户名\",\n        password: \"密码\",\n        confirmPassword: \"确认密码\",\n        needAccount: \"没有账户？注册\",\n        haveAccount: \"已有账户？登录\",\n        loginRequired: \"需要登录\",\n        loginToViewHistory: \"请登录查看测验历史\",\n        // User Stats\n        stats: \"统计\",\n        totalQuestions: \"总题数\",\n        accuracy: \"准确率\",\n        streak: \"连续正确\",\n        viewHistory: \"查看历史\",\n        // History\n        quizHistory: \"测验历史\",\n        backToQuiz: \"返回测验\",\n        allLevels: \"所有级别\",\n        allResults: \"所有结果\",\n        result: \"结果\",\n        noHistory: \"未找到测验历史\",\n        previous: \"上一页\",\n        next: \"下一页\",\n        // AI Features\n        aiLessons: \"AI课程\",\n        aiReading: \"AI阅读\",\n        aiTutor: \"AI导师\",\n        createLesson: \"创建课程\",\n        generateReading: \"生成阅读\",\n        askTutor: \"询问AI导师\",\n        // Lessons\n        lessonTitle: \"AI英语课程\",\n        lessonSubtitle: \"使用AI创建个性化英语课程。选择您的主题、级别和课程类型来生成全面的学习内容。\",\n        lessonType: \"课程类型\",\n        duration: \"时长\",\n        topic: \"主题\",\n        topicPlaceholder: \"例如：一般现在时、商务英语、旅游词汇...\",\n        topicSuggestions: \"或从热门主题中选择：\",\n        generateLesson: \"生成课程\",\n        generating: \"生成中...\",\n        // Reading\n        readingTitle: \"AI阅读练习\",\n        readingSubtitle: \"使用AI生成个性化阅读文章。选择您的级别、主题和风格来创建无限练习内容。\",\n        wordCount: \"字数\",\n        style: \"风格\",\n        generatePassage: \"生成文章\",\n        vocabulary: \"词汇\",\n        comprehensionQuestions: \"理解问题\",\n        submitAnswers: \"提交答案\",\n        tryAgain: \"重试\",\n        generateNew: \"生成新文章\",\n        // Dashboard\n        dashboardTitle: \"学习仪表板\",\n        dashboardSubtitle: \"跟踪您的进度，分析您的表现，并获得个性化建议来提高您的英语。\",\n        quizzesTaken: \"已完成测验\",\n        overallPerformance: \"整体表现\",\n        streakDays: \"连续天数\",\n        studyTime: \"学习时间\",\n        progressByLevel: \"按级别进度\",\n        aiAnalysis: \"AI表现分析\",\n        strengths: \"优势\",\n        improvements: \"需要改进\",\n        nextSteps: \"下一步\",\n        motivationalMessage: \"激励信息\",\n        continuelearning: \"继续学习\",\n        // Tutor\n        tutorTitle: \"AI英语导师\",\n        tutorSubtitle: \"获得语法、词汇、发音等方面的即时帮助。用您的母语提问。\",\n        askQuestion: \"提问...\",\n        send: \"发送\",\n        grammarHelp: \"语法帮助\",\n        vocabularyHelp: \"词汇\",\n        pronunciationHelp: \"发音\",\n        writingHelp: \"写作帮助\",\n        conversationHelp: \"对话\",\n        cultureHelp: \"文化\",\n        // Footer\n        footerDescription: \"使用我们专为越南学习者设计的AI平台免费学习英语。无限选择题、结构化课程、词汇和有效的学习技巧。适合从基础到高级的所有水平。\",\n        contactAdvertising: \"广告联系\",\n        quickLinks: \"快速链接\",\n        about: \"关于我们\",\n        contact: \"联系我们\",\n        legal: \"法律\",\n        terms: \"服务条款\",\n        privacy: \"隐私政策\",\n        allRightsReserved: \"版权所有。\",\n        disclaimer: \"免责声明\",\n        disclaimerText: \"我们的网站仅提供在线英语学习服务，用于娱乐和内容分享目的。网站上发布的所有学习内容均来自互联网上的各种来源，我们对任何内容的版权或所有权不承担责任。如果您是版权所有者并认为网站上的内容侵犯了您的权利，请联系我们及时删除侵权内容。此外，我们对网站上显示的广告内容不承担责任，包括但不限于第三方产品或服务的广告。这些广告不代表我们的观点或承诺。用户在与此类广告互动时需要自行考虑并承担责任。\",\n        // Common\n        level: \"级别\",\n        back: \"返回\",\n        continue: \"继续\",\n        complete: \"完成\",\n        start: \"开始\",\n        finish: \"结束\",\n        save: \"保存\",\n        cancel: \"取消\",\n        close: \"关闭\",\n        signIn: \"登录\",\n        signOut: \"退出\",\n        signUp: \"注册\",\n        // Auth Modal\n        continueWithGoogle: \"使用Google继续\",\n        continueWithFacebook: \"使用Facebook继续\",\n        optionalLoginNote: \"登录是可选的。您可以在没有账户的情况下继续，但您的进度不会被保存。\",\n        loginError: \"使用{provider}登录失败。请重试。\",\n        // Language Switcher\n        active: \"活跃\",\n        // User Menu\n        learningDashboard: \"学习仪表板\",\n        accuracyText: \"准确率\",\n        // Error Messages\n        aiErrorMessage: \"抱歉，处理您的问题时遇到错误。请重试。\",\n        failedToGenerate: \"生成问题失败\",\n        questionGenerateError: \"抱歉，我们目前无法生成问题。请稍后重试。\",\n        tryAgainLater: \"稍后重试\",\n        backToHome: \"返回首页\",\n        // Loading States\n        processing: \"处理中...\",\n        pleaseWait: \"请稍候...\",\n        loadingHistory: \"正在加载您的测验历史...\",\n        // General Error States\n        somethingWentWrong: \"出现问题\",\n        errorOccurred: \"发生错误\",\n        retryAction: \"重试\",\n        // Additional Reading Component\n        aiGenerates: \"AI生成\",\n        aiGeneratesDesc: \"我们的AI创建独特的文章和问题\",\n        failedToGeneratePassage: \"生成阅读文章失败。请重试。\",\n        // Additional Lessons Component\n        aiCreates: \"AI创建\",\n        aiCreatesDesc: \"AI生成全面的课程内容\",\n        failedToGenerateLesson: \"生成课程失败。请重试。\",\n        // History Page\n        noHistoryYet: \"暂无历史记录\",\n        // Question Card\n        processingAnswer: \"正在处理您的答案...\",\n        chooseAnswer: \"选择您的答案\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/i18n/translations.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CAI%5Cwebngonngu%5Cinfinite-language&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();