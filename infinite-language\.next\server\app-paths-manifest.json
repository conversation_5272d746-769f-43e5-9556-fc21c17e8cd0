{"/api/vocabulary/topics/route": "app/api/vocabulary/topics/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/_not-found/page": "app/_not-found/page.js", "/page": "app/page.js", "/grammar/page": "app/grammar/page.js", "/reading/page": "app/reading/page.js", "/vocabulary/page": "app/vocabulary/page.js", "/tutor/page": "app/tutor/page.js", "/blog/page": "app/blog/page.js"}