import { Metadata } from 'next';
import VocabularyClient from '@/components/VocabularyClient';

export const metadata: Metadata = {
  title: 'Vocabulary Topics - Infinite English',
  description: 'Explore English vocabulary by topics. Learn new words with definitions, examples, and pronunciation guides for all levels.',
  keywords: 'English vocabulary, vocabulary topics, learn English words, vocabulary practice, English learning, word meanings',
  openGraph: {
    title: 'Vocabulary Topics - Infinite English',
    description: 'Explore English vocabulary by topics with interactive learning tools.',
    type: 'website',
  },
};

export default function VocabularyPage() {
  return <VocabularyClient />;
}
