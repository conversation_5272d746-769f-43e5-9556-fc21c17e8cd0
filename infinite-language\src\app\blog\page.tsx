import { Metadata } from 'next';
import BlogClient from '@/components/BlogClient';

export const metadata: Metadata = {
  title: 'English Learning Blog - Infinite English',
  description: 'Read helpful articles, tips, and guides for learning English. Improve your English skills with expert advice and practical tips.',
  keywords: 'English learning blog, English tips, language learning, English study tips, English improvement, learning English',
  openGraph: {
    title: 'English Learning Blog - Infinite English',
    description: 'Read helpful articles and tips for learning English effectively.',
    type: 'website',
  },
};

export default function BlogPage() {
  return <BlogClient />;
}
